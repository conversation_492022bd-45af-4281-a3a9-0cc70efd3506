---
# Variables for media_miner playbook

# Feature flags - enable/disable components
enable_readarr: false
enable_bazarr: false
enable_encoding: false

# Encoding application selection (tdarr, unmanic, or fileflows)
encoding_application: "fileflows"

# Container configuration
container_user_id: "1000"
container_group_id: "1000"

# Docker Hub authentication
docker_hub_login: false
docker_hub_default_user: krizzo

# Service ports
sonarr_port: 8989
radarr_port: 7878
lidarr_port: 8686
readarr_port: 8787
bazarr_port: 6767
prowlarr_port: 9696
nzbget_port: 6789
tdarr_port: 8265
unmanic_port: 8888
fileflows_port: 5000
overseerr: 5055