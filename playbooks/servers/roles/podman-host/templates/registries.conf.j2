# /etc/containers/registries.conf
# This file is managed by Ansible - do not edit manually

# Include all configuration files from registries.conf.d directory
# This allows for modular registry configuration
# Files in registries.conf.d are processed in alphabetical order

# Search registries for unqualified images
[registries.search]
unqualified-search-registries = [{% for registry in podman_registries %}"{{ registry }}"{% if not loop.last %}, {% endif %}{% endfor %}]

# Registry configurations
{% for registry in podman_registries %}
[[registry]]
location = "{{ registry }}"
{% if registry == "docker.io" %}
# Docker Hub registry configuration
# Note: Additional docker.io configurations may be present in registries.conf.d/
{% elif registry == "ghcr.io" %}
# GitHub Container Registry configuration
# Note: Additional ghcr.io configurations may be present in registries.conf.d/
{% elif registry == "lscr.io" %}
# LinuxServer.io Container Registry configuration
{% endif %}

{% endfor %}
