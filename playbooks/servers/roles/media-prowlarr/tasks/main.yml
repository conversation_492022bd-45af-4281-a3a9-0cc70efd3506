---
# tasks file for media-prowlarr

- name: "Mount {{ item.name }}"
  ansible.posix.mount:
    src: "{{ item.src }}"
    path: "{{ item.path }}"
    opts: "{{ item.opts }}"
    state: mounted
    fstype: "{{ item.fstype }}"
  loop: "{{ nfs_mounts }}"

- name: Create local directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: directory
    mode: "{{ item.mode | default('0755') }}"
    owner: "{{ container_user_id }}"
    group: "{{ container_group_id }}"
  loop: "{{ app_paths }}"

- name: Configure firewall rules for Prowlarr
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ prowlarr_port }}"
      protocol: tcp
      comment: "expose port for prowlarr ui http"

- name: Pull the latest Prowlarr image
  containers.podman.podman_image:
    name: "{{ prowlarr_image }}"
  register: prowlarr_image_pull

- name: Stop and remove existing prowlarr container
  containers.podman.podman_container:
    name: prowlarr
    state: absent
  when: prowlarr_image_pull.changed

# Indexer management app
- name: Start prowlarr container
  containers.podman.podman_container:
    name: prowlarr
    image: "{{ prowlarr_image }}"
    state: started
    restart_policy: "on-failure:5"
    network: bridge
    ports:
      - "{{ prowlarr_port }}:{{ prowlarr_port }}"
    volumes: "{{ prowlarr_volumes }}"
    env: "{{ prowlarr_environment }}"
