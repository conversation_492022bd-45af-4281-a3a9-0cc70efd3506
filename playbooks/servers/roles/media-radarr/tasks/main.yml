---
# tasks file for media-radarr

- name: "Mount {{ item.name }}"
  ansible.posix.mount:
    src: "{{ item.src }}"
    path: "{{ item.path }}"
    opts: "{{ item.opts }}"
    state: mounted
    fstype: "{{ item.fstype }}"
  loop: "{{ nfs_mounts }}"

- name: Create local directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: directory
    mode: "{{ item.mode | default('0755') }}"
    owner: "{{ container_user_id }}"
    group: "{{ container_group_id }}"
  loop: "{{ app_paths }}"

- name: Configure firewall rules for Radarr
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ radarr_port }}"
      protocol: tcp
      comment: "expose port for radarr ui http"

- name: Pull the latest Radarr image
  containers.podman.podman_image:
    name: "{{ radarr_image }}"
  register: radarr_image_pull

- name: Stop and remove existing radarr container
  containers.podman.podman_container:
    name: radarr
    state: absent
  when: radarr_image_pull.changed

# Movie Search App
- name: Start radarr container
  containers.podman.podman_container:
    name: radarr
    image: "{{ radarr_image }}"
    state: started
    restart_policy: "on-failure:5"
    network: bridge
    ports:
      - "{{ radarr_port }}:{{ radarr_port }}"
    volumes: "{{ radarr_volumes }}"
    env: "{{ radarr_environment }}"
