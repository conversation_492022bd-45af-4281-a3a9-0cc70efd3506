---
# tasks file for notifiarr

- name: "Mount {{ item.name }}"
  ansible.posix.mount:
    src: "{{ item.src }}"
    path: "{{ item.path }}"
    opts: "{{ item.opts }}"
    state: mounted
    fstype: "{{ item.fstype }}"
  loop: "{{ nfs_mounts }}"

- name: Create local directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: directory
    mode: "{{ item.mode | default('0755') }}"
    owner: "{{ container_user_id }}"
    group: "{{ container_group_id }}"
  loop: "{{ app_paths }}"

- name: Configure firewall rules for Notifiarr
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ notifiarr_port }}"
      protocol: tcp
      comment: "expose port for notifiarr ui http"

- name: Pull the latest Notifiarr image
  containers.podman.podman_image:
    name: "{{ notifiarr_image }}"
  register: notifiarr_image_pull

- name: Stop and remove existing Notifiarr container
  containers.podman.podman_container:
    name: notifiarr
    state: absent
  when: notifiarr_image_pull.changed

# TV Series Search App
- name: Start Notifiarr container
  containers.podman.podman_container:
    name: notifiarr
    hostname: notifiarr0
    image: "{{ notifiarr_image }}"
    state: started
    restart_policy: "on-failure:5"
    network: bridge
    ports:
      - "{{ notifiarr_port }}:{{ notifiarr_port }}"
    volumes: "{{ notifiarr_volumes }}"
    env: "{{ notifiarr_environment }}"
